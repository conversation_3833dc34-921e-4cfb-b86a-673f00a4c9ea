<template>
  <div class="local-image-inpainting" v-if="visible">
    <div class="inpainting-backdrop" @click="handleClose"></div>
    <div class="inpainting-container">
      <div class="inpainting-header">
        <h3>局部重绘</h3>
        <el-icon class="close-icon" @click="handleClose">
          <Close />
        </el-icon>
      </div>

      <div class="inpainting-content">
        <!-- 主要图片显示区域 -->
        <div class="main-image-area">
          <div class="canvas-container" v-loading="loading">
            <div class="canvas-wrapper">
              <!-- 图片预览层 -->
              <img :src="imageUrl" alt="原图" v-if="imageUrl" class="original-image" ref="originalImage" />

              <!-- 绘制层 -->
              <canvas ref="maskCanvas" class="mask-canvas" @mousedown="startDrawing"
                @mousemove="(e) => { draw(e); updateCursorPosition(e); }" @mouseup="stopDrawing"
                @mouseleave="stopDrawing" @touchstart="startDrawingTouch" @touchmove="drawTouch" @touchend="stopDrawing"
                @touchcancel="stopDrawing"></canvas>

              <!-- 自定义光标 -->
              <div class="custom-cursor" ref="customCursor"></div>
            </div>
          </div>
        </div>

        <!-- 底部控制区域 -->
        <div class="bottom-controls">
          <!-- 左侧工具栏 -->
          <div class="left-tools">
            <div class="tool-group">
              <div class="tool-button" :class="{ active: toolMode === 'brush' }" @click="toolMode = 'brush'">
                <el-icon>
                  <Brush />
                </el-icon>
              </div>
              <div class="tool-button" :class="{ active: toolMode === 'eraser' }" @click="toolMode = 'eraser'">
                <el-icon>
                  <Delete />
                </el-icon>
              </div>
              <div class="tool-button" @click="undoLastDraw" :disabled="drawActions.length === 0 || loading">
                <el-icon>
                  <Back />
                </el-icon>
              </div>
              <div class="tool-button" @click="clearCanvas" :disabled="loading">
                <el-icon>
                  <Delete />
                </el-icon>
              </div>
            </div>
          </div>

          <!-- 中间缩放控制 -->
          <div class="zoom-controls">
            <div class="zoom-button" @click="zoomOut">-</div>
            <span class="zoom-text">100%</span>
            <div class="zoom-button" @click="zoomIn">+</div>
          </div>

          <!-- 右侧生成按钮 -->
          <div class="right-actions">
            <button class="generate-button" @click="handleGenerate" :disabled="loading">
              立即生成 ⚡ 1
            </button>
          </div>
        </div>

        <!-- 底部提示词输入 -->
        <div class="prompt-section">
          <el-input
            v-model="inpaintPrompt"
            placeholder="描述想要重新绘制的内容，不填将基于原图生成"
            :disabled="loading"
            class="prompt-input"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, watch } from 'vue';
import { Close, Delete, Brush, Back } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { uploadToOSS } from '@/api/oss.js';
import { submitImageInpainting } from '@/api/image.js';
import { generateCanvasImage } from '@/api/auth.js';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  imageUrl: {
    type: String,
    default: ''
  },
  conversationId: {
    type: String,
    default: ''
  },
  shotId: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['close', 'update']);

// 提示词
const inpaintPrompt = ref('');

// 加载状态
const loading = ref(false);

// 画布相关
const originalImage = ref(null);
const maskCanvas = ref(null);
const maskCtx = ref(null);
const isDrawing = ref(false);
const lastX = ref(0);
const lastY = ref(0);
const brushSize = ref(20);
const drawActions = ref([]);
const canvasInfo = ref(null);
const customCursor = ref(null); // 自定义光标引用

// 添加工具模式
const toolMode = ref('brush'); // 'brush' 或 'eraser'

// 画笔颜色相关
const brushColors = [
  { name: '红色', value: 'rgba(255, 0, 0, 0.4)' },
  { name: '蓝色', value: 'rgba(0, 0, 255, 0.4)' },
  { name: '绿色', value: 'rgba(0, 255, 0, 0.4)' },
  { name: '黄色', value: 'rgba(255, 255, 0, 0.4)' }
];
const brushColor = ref(brushColors[0].value);

// 是否有涂抹区域
const hasMaskArea = computed(() => {
  return drawActions.value.length > 0;
});

// 是否可以应用重绘
const canApplyInpainting = computed(() => {
  return inpaintPrompt.value.trim() !== '' && drawActions.value.length > 0;
});

// 是否可以智能修图
const canSmartEdit = computed(() => {
  return inpaintPrompt.value.trim() !== '' && drawActions.value.length === 0;
});

// 缩放相关
const zoomLevel = ref(100);

// 缩放方法
const zoomIn = () => {
  if (zoomLevel.value < 200) {
    zoomLevel.value += 10;
  }
};

const zoomOut = () => {
  if (zoomLevel.value > 50) {
    zoomLevel.value -= 10;
  }
};

// 生成处理方法
const handleGenerate = () => {
  if (hasMaskArea.value) {
    applyInpainting();
  } else {
    smartEditImage();
  }
};

// 轮询相关
const currentInpaintingCode = ref('');
const pollingTimer = ref(null);

// 初始化画布
const initCanvas = () => {
  if (!maskCanvas.value || !props.imageUrl) return;

  console.log('初始化画布开始');

  // 等待原始图片加载完成
  const waitForOriginalImage = () => {
    if (!originalImage.value) {
      console.log('原始图片元素不存在，等待DOM更新');
      setTimeout(waitForOriginalImage, 50);
      return;
    }

    if (!originalImage.value.complete) {
      console.log('原始图片尚未加载完成，等待加载');
      originalImage.value.onload = initCanvasAfterImageLoaded;
      return;
    }

    initCanvasAfterImageLoaded();
  };

  // 图片加载完成后初始化画布
  const initCanvasAfterImageLoaded = () => {
    if (!originalImage.value) {
      console.error('原始图片元素不存在');
      return;
    }

    console.log('原始图片尺寸:', originalImage.value.naturalWidth, 'x', originalImage.value.naturalHeight);
    console.log('显示尺寸:', originalImage.value.clientWidth, 'x', originalImage.value.clientHeight);

    // 使用图片的实际显示尺寸
    const displayWidth = originalImage.value.clientWidth;
    const displayHeight = originalImage.value.clientHeight;

    // 设置canvas的CSS尺寸与图片显示尺寸相同
    maskCanvas.value.style.width = `${displayWidth}px`;
    maskCanvas.value.style.height = `${displayHeight}px`;

    // 获取设备像素比
    const dpr = window.devicePixelRatio || 1;
    console.log('设备像素比:', dpr);

    // 设置canvas的实际尺寸（考虑设备像素比以提高清晰度）
    maskCanvas.value.width = displayWidth * dpr;
    maskCanvas.value.height = displayHeight * dpr;

    // 获取画布上下文
    maskCtx.value = maskCanvas.value.getContext('2d');

    // 缩放上下文以匹配设备像素比
    maskCtx.value.scale(dpr, dpr);

    // 设置画布背景为透明
    maskCtx.value.clearRect(0, 0, maskCanvas.value.width, maskCanvas.value.height);

    // 保存画布信息
    canvasInfo.value = {
      width: displayWidth,
      height: displayHeight,
      dpr: dpr,
      originalWidth: originalImage.value.naturalWidth,
      originalHeight: originalImage.value.naturalHeight
    };

    // 清空绘画历史
    drawActions.value = [];

    // 确保画布可见
    maskCanvas.value.style.opacity = '1';
    maskCanvas.value.style.pointerEvents = 'auto';

    console.log('画布初始化完成');
  };

  // 开始等待原始图片
  waitForOriginalImage();
};

// 开始绘制（鼠标）
const startDrawing = (e) => {
  console.log('开始绘制', e.type);
  isDrawing.value = true;

  // 保存当前画布状态，用于撤销
  saveCanvasState();

  // 获取鼠标相对于画布的位置
  const rect = maskCanvas.value.getBoundingClientRect();
  const canvasX = (e.clientX - rect.left);
  const canvasY = (e.clientY - rect.top);

  console.log('鼠标位置:', canvasX, canvasY);

  lastX.value = canvasX;
  lastY.value = canvasY;

  // 单点绘制，避免零长度线段
  drawDot(canvasX, canvasY);
};

// 绘制（鼠标）
const draw = (e) => {
  if (!isDrawing.value) return;

  // 获取鼠标当前位置
  const rect = maskCanvas.value.getBoundingClientRect();
  const canvasX = (e.clientX - rect.left);
  const canvasY = (e.clientY - rect.top);

  console.log('绘制中', canvasX, canvasY);

  // 绘制线条
  drawLine(lastX.value, lastY.value, canvasX, canvasY);

  // 更新上一次位置
  lastX.value = canvasX;
  lastY.value = canvasY;
};

// 开始绘制（触摸）
const startDrawingTouch = (e) => {
  isDrawing.value = true;

  // 阻止默认行为（如滚动）
  e.preventDefault();

  // 保存当前画布状态，用于撤销
  saveCanvasState();

  // 获取触摸点相对于画布的位置
  const rect = maskCanvas.value.getBoundingClientRect();
  const touch = e.touches[0];
  const canvasX = (touch.clientX - rect.left);
  const canvasY = (touch.clientY - rect.top);

  lastX.value = canvasX;
  lastY.value = canvasY;

  // 单点绘制，避免零长度线段
  drawDot(canvasX, canvasY);
};

// 绘制（触摸）
const drawTouch = (e) => {
  if (!isDrawing.value) return;

  // 阻止默认行为（如滚动）
  e.preventDefault();

  // 获取触摸点当前位置
  const rect = maskCanvas.value.getBoundingClientRect();
  const touch = e.touches[0];
  const canvasX = (touch.clientX - rect.left);
  const canvasY = (touch.clientY - rect.top);

  // 绘制线条
  drawLine(lastX.value, lastY.value, canvasX, canvasY);

  // 更新上一次位置
  lastX.value = canvasX;
  lastY.value = canvasY;
};

// 单点绘制
const drawDot = (x, y) => {
  if (!maskCtx.value) {
    console.error('画布上下文不存在');
    return;
  }

  console.log('绘制点', x, y);

  // 保存当前上下文状态
  maskCtx.value.save();

  if (toolMode.value === 'brush') {
    // 绘制模式
    // 设置合成模式为"xor"，这样重叠区域不会增加透明度
    maskCtx.value.globalCompositeOperation = 'xor';

    // 使用固定不透明度的红色
    maskCtx.value.fillStyle = brushColor.value;
  } else {
    // 橡皮擦模式
    maskCtx.value.globalCompositeOperation = 'destination-out';
    maskCtx.value.fillStyle = 'rgba(0, 0, 0, 1)';
  }

  // 绘制一个圆点
  maskCtx.value.beginPath();
  maskCtx.value.arc(x, y, brushSize.value / 2, 0, Math.PI * 2);
  maskCtx.value.fill();
  maskCtx.value.closePath();

  // 恢复上下文状态
  maskCtx.value.restore();
};

// 绘制线条
const drawLine = (x1, y1, x2, y2) => {
  if (!maskCtx.value) {
    console.error('画布上下文不存在');
    return;
  }

  console.log('绘制线条', x1, y1, 'to', x2, y2);

  // 保存当前上下文状态
  maskCtx.value.save();

  if (toolMode.value === 'brush') {
    // 绘制模式
    // 设置合成模式为"xor"，这样重叠区域不会增加透明度
    maskCtx.value.globalCompositeOperation = 'xor';
    maskCtx.value.strokeStyle = brushColor.value;
  } else {
    // 橡皮擦模式
    maskCtx.value.globalCompositeOperation = 'destination-out';
    maskCtx.value.strokeStyle = 'rgba(0, 0, 0, 1)';
  }

  maskCtx.value.lineWidth = brushSize.value;
  maskCtx.value.lineCap = 'round';
  maskCtx.value.lineJoin = 'round';

  // 绘制线条
  maskCtx.value.beginPath();
  maskCtx.value.moveTo(x1, y1);
  maskCtx.value.lineTo(x2, y2);
  maskCtx.value.stroke();
  maskCtx.value.closePath();

  // 恢复上下文状态
  maskCtx.value.restore();
};

// 停止绘制
const stopDrawing = () => {
  if (isDrawing.value) {
    console.log('停止绘制');
    // 绘制结束时更新预览
    updateMaskPreview();
  }
  isDrawing.value = false;
};

// 保存画布状态
const saveCanvasState = () => {
  if (!maskCanvas.value) return;
  drawActions.value.push(maskCanvas.value.toDataURL());
};

// 撤销最后一次绘制
const undoLastDraw = () => {
  if (drawActions.value.length === 0) return;

  // 移除最后一次绘制的状态
  drawActions.value.pop();

  // 清空画布
  clearCanvas();

  // 如果还有之前的绘制操作，恢复到上一个状态
  if (drawActions.value.length > 0) {
    const lastState = drawActions.value[drawActions.value.length - 1];
    const img = new Image();
    img.onload = () => {
      if (!canvasInfo.value) return;

      // 绘制上一个状态
      maskCtx.value.drawImage(img, 0, 0, canvasInfo.value.width, canvasInfo.value.height);
      // 更新预览
      updateMaskPreview();
    };
    img.src = lastState;
  } else {
    // 如果没有历史状态，直接更新预览（显示空白遮罩）
    updateMaskPreview();
  }
};

// 清除画布
const clearCanvas = () => {
  if (!maskCanvas.value || !canvasInfo.value) return;

  // 清空画布
  maskCtx.value.clearRect(0, 0, maskCanvas.value.width / canvasInfo.value.dpr, maskCanvas.value.height / canvasInfo.value.dpr);

  // 重置绘画历史
  drawActions.value = [];

  // 更新预览
  updateMaskPreview();
};

// 生成遮罩图片（用于上传和API调用）
const generateMaskImage = async () => {
  if (!maskCanvas.value || !canvasInfo.value) return null;

  console.log('生成遮罩图片 - 画布信息:', {
    canvasWidth: maskCanvas.value.width,
    canvasHeight: maskCanvas.value.height,
    displayWidth: canvasInfo.value.width,
    displayHeight: canvasInfo.value.height,
    originalWidth: canvasInfo.value.originalWidth,
    originalHeight: canvasInfo.value.originalHeight,
    dpr: canvasInfo.value.dpr
  });

  // 创建临时画布，确保与原图分辨率完全一致
  const tempCanvas = document.createElement('canvas');
  tempCanvas.width = canvasInfo.value.originalWidth;
  tempCanvas.height = canvasInfo.value.originalHeight;
  const tempCtx = tempCanvas.getContext('2d');

  // 设置高质量渲染
  tempCtx.imageSmoothingEnabled = true;
  tempCtx.imageSmoothingQuality = 'high';

  // 清空临时画布，设置为黑色背景
  tempCtx.fillStyle = 'black';
  tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

  // 将当前画布内容精确缩放到原始图片尺寸
  // 画布的实际尺寸是 width * dpr 和 height * dpr
  tempCtx.drawImage(
    maskCanvas.value,
    0, 0, maskCanvas.value.width, maskCanvas.value.height,
    0, 0, canvasInfo.value.originalWidth, canvasInfo.value.originalHeight
  );

  // 获取图像数据进行二值化处理
  const imageData = tempCtx.getImageData(0, 0, tempCanvas.width, tempCanvas.height);
  const data = imageData.data;

  // 将有颜色的区域转换为白色，其他区域保持黑色
  for (let i = 0; i < data.length; i += 4) {
    const r = data[i];
    const g = data[i + 1];
    const b = data[i + 2];
    const a = data[i + 3];

    // 如果像素有颜色且不透明度大于阈值（表示被画笔标记）
    if (a > 50 && (r > 50 || g > 50 || b > 50)) {
      // 设置为白色（遮罩区域）
      data[i] = 255;     // R
      data[i + 1] = 255; // G
      data[i + 2] = 255; // B
      data[i + 3] = 255; // A (完全不透明)
    } else {
      // 设置为黑色（非遮罩区域）
      data[i] = 0;       // R
      data[i + 1] = 0;   // G
      data[i + 2] = 0;   // B
      data[i + 3] = 255; // A (完全不透明)
    }
  }

  // 将处理后的图像数据放回画布
  tempCtx.putImageData(imageData, 0, 0);

  // 将画布内容转换为Blob对象
  return new Promise((resolve) => {
    tempCanvas.toBlob((blob) => {
      resolve(blob);
    }, 'image/png', 1.0); // 使用最高质量
  });
};

// 生成预览用的遮罩图片（用于右侧面板显示）
const generatePreviewMaskImage = async () => {
  if (!maskCanvas.value || !canvasInfo.value) return null;

  // 创建预览画布，使用较小的尺寸以提高性能
  const previewCanvas = document.createElement('canvas');
  const previewWidth = 300; // 固定预览宽度
  const aspectRatio = canvasInfo.value.originalHeight / canvasInfo.value.originalWidth;
  const previewHeight = Math.round(previewWidth * aspectRatio);

  previewCanvas.width = previewWidth;
  previewCanvas.height = previewHeight;
  const previewCtx = previewCanvas.getContext('2d');

  // 设置高质量渲染
  previewCtx.imageSmoothingEnabled = true;
  previewCtx.imageSmoothingQuality = 'high';

  // 清空预览画布，设置为黑色背景
  previewCtx.fillStyle = 'black';
  previewCtx.fillRect(0, 0, previewWidth, previewHeight);

  // 将当前画布内容缩放到预览尺寸
  previewCtx.drawImage(
    maskCanvas.value,
    0, 0, maskCanvas.value.width, maskCanvas.value.height,
    0, 0, previewWidth, previewHeight
  );

  // 获取图像数据进行二值化处理
  const imageData = previewCtx.getImageData(0, 0, previewWidth, previewHeight);
  const data = imageData.data;

  // 将有颜色的区域转换为白色，其他区域保持黑色
  for (let i = 0; i < data.length; i += 4) {
    const r = data[i];
    const g = data[i + 1];
    const b = data[i + 2];
    const a = data[i + 3];

    // 如果像素有颜色且不透明度大于阈值
    if (a > 50 && (r > 50 || g > 50 || b > 50)) {
      // 设置为白色（遮罩区域）
      data[i] = 255;     // R
      data[i + 1] = 255; // G
      data[i + 2] = 255; // B
      data[i + 3] = 255; // A
    } else {
      // 设置为黑色（非遮罩区域）
      data[i] = 0;       // R
      data[i + 1] = 0;   // G
      data[i + 2] = 0;   // B
      data[i + 3] = 255; // A
    }
  }

  // 将处理后的图像数据放回画布
  previewCtx.putImageData(imageData, 0, 0);

  // 将画布内容转换为Blob URL用于预览
  return new Promise((resolve) => {
    previewCanvas.toBlob((blob) => {
      if (blob) {
        const url = URL.createObjectURL(blob);
        resolve(url);
      } else {
        resolve(null);
      }
    }, 'image/png', 0.8); // 预览使用较低质量以提高性能
  });
};

// 遮罩预览相关
const maskBlob = ref(null);
const maskPreviewUrl = ref(null);

// 更新遮罩预览
const updateMaskPreview = async () => {
  try {
    // 清理之前的预览URL
    if (maskPreviewUrl.value) {
      URL.revokeObjectURL(maskPreviewUrl.value);
    }

    // 生成新的预览
    const previewUrl = await generatePreviewMaskImage();
    maskPreviewUrl.value = previewUrl;
  } catch (error) {
    console.error('更新遮罩预览失败:', error);
  }
};

// 智能修图
const smartEditImage = async () => {
  if (!canSmartEdit.value || loading.value) return;

  loading.value = true;

  try {
    // 处理参考图URL，去除协议和域名部分
    let referenceImageUrl = props.imageUrl || '';
    // 检查是否以 http:// 或 https:// 开头
    if (/^https?:\/\//.test(referenceImageUrl)) {
      // 去除协议和域名部分
      referenceImageUrl = referenceImageUrl.replace(/^https?:\/\/[^\/]+/, '');
    }

    // 准备API参数，参考 ImageGenerationPanel.vue 中的 generateImage 函数
    const params = {
      prompt: inpaintPrompt.value.trim(),
      referenceImageUrl: referenceImageUrl,
      aspectRatio: '16:9', // 默认比例，可以根据需要调整
      strength: 5.0, // 默认强度，可以根据需要调整
      shotId: props.shotId || ''
    };

    console.log('提交智能修图请求:', params);

    // 调用API
    ElMessage.info('正在生成图片，请稍候...');
    const response = await generateCanvasImage(params);

    if (response.success) {
      ElMessage.success('图片生成任务已提交');
      // 通知父组件更新
      emit('update');
      // 关闭弹窗
      handleClose();
    } else {
      throw new Error(response.errMessage || '图片生成失败');
    }
  } catch (error) {
    console.error('智能修图失败:', error);
    ElMessage.error('智能修图失败，请重试');
    loading.value = false;
  }
};

// 应用局部重绘
const applyInpainting = async () => {
  if (!canApplyInpainting.value || loading.value) return;

  loading.value = true;

  try {
    // 生成遮罩图片
    const generatedMaskBlob = await generateMaskImage();

    if (!generatedMaskBlob) {
      throw new Error('生成遮罩图片失败');
    }

    maskBlob.value = generatedMaskBlob;

    // 上传遮罩图片到OSS
    const maskResult = await uploadToOSS(maskBlob.value, props.conversationId || 'inpainting');
    if (!maskResult || !maskResult.url) {
      throw new Error('上传遮罩图片失败');
    }

    // 构建请求参数
    const requestData = {
      originalImageUrl: props.imageUrl,
      maskImageUrl: maskResult.url,
      prompt: inpaintPrompt.value.trim(),
      shotId: props.shotId || '',
    };

    console.log('提交局部重绘请求:', requestData);

    // 提交局部重绘请求
    const result = await submitImageInpainting(requestData);

    if (result) {
      handleClose();
    } else {
      throw new Error('提交局部重绘请求失败，未获取到处理码');
    }
  } catch (error) {
    console.error('局部重绘失败:', error);
    ElMessage.error('局部重绘失败，请重试');
    loading.value = false;
  }
};



// 关闭组件
const handleClose = () => {
  // 清除轮询定时器
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value);
  }

  // 清理预览URL
  if (maskPreviewUrl.value) {
    URL.revokeObjectURL(maskPreviewUrl.value);
    maskPreviewUrl.value = null;
  }

  // 重置状态
  loading.value = false;
  inpaintPrompt.value = '';
  drawActions.value = [];
  currentInpaintingCode.value = '';
  maskBlob.value = null;

  // 通知父组件关闭
  emit('close');
};

// 监听可见性变化
watch(() => props.visible, (newVisible) => {
  console.log('可见性变化', newVisible);
  if (newVisible) {
    // 组件显示时初始化画布
    setTimeout(() => {
      console.log('延时初始化画布');
      initCanvas();
    }, 100);
  }
});

// 监听图片URL变化
watch(() => props.imageUrl, (newUrl) => {
  console.log('图片URL变化', newUrl);
  if (props.visible && newUrl) {
    // 图片URL变化时重新初始化画布
    setTimeout(() => {
      console.log('图片变化，重新初始化画布');
      initCanvas();
    }, 100);
  }
});

// 更新光标位置和大小
const updateCursorPosition = (e) => {
  if (!customCursor.value || !maskCanvas.value) return;

  const rect = maskCanvas.value.getBoundingClientRect();
  const x = e.clientX;
  const y = e.clientY;

  // 更新光标位置
  customCursor.value.style.left = `${x}px`;
  customCursor.value.style.top = `${y}px`;

  // 检查鼠标是否在画布上
  const isOnCanvas =
    x >= rect.left &&
    x <= rect.right &&
    y >= rect.top &&
    y <= rect.bottom;

  // 显示/隐藏光标
  customCursor.value.style.display = isOnCanvas ? 'block' : 'none';
};

// 更新光标大小和样式
const updateCursorSize = () => {
  if (!customCursor.value) return;

  // 设置光标大小为笔刷大小
  const size = brushSize.value;
  customCursor.value.style.width = `${size}px`;
  customCursor.value.style.height = `${size}px`;

  // 根据工具模式设置不同的光标样式
  if (toolMode.value === 'brush') {
    customCursor.value.style.border = '2px solid #fff';
    customCursor.value.style.backgroundColor = 'rgba(255, 0, 0, 0.2)';
    customCursor.value.style.boxShadow = '0 0 0 1px rgba(0, 0, 0, 0.5)';
  } else {
    customCursor.value.style.border = '2px solid #000';
    customCursor.value.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
    customCursor.value.style.boxShadow = '0 0 0 1px rgba(255, 255, 255, 0.5)';
  }
};

// 监听笔刷大小变化
watch(() => brushSize.value, () => {
  updateCursorSize();
});

// 监听工具模式变化
watch(() => toolMode.value, () => {
  updateCursorSize();
});

// 组件挂载时
onMounted(() => {
  console.log('组件挂载');
  if (props.visible && props.imageUrl) {
    // 初始化画布
    setTimeout(() => {
      console.log('挂载时初始化画布');
      initCanvas();
      // 初始化光标大小
      updateCursorSize();
    }, 100);
  }

  // 添加鼠标移出窗口事件监听
  window.addEventListener('mouseout', handleMouseOut);
});

// 处理鼠标移出窗口
const handleMouseOut = (e) => {
  if (!customCursor.value) return;

  // 如果鼠标移出窗口，隐藏光标
  if (e.relatedTarget === null) {
    customCursor.value.style.display = 'none';
  }
};

// 组件卸载前
onBeforeUnmount(() => {
  // 清除轮询定时器
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value);
  }

  // 移除鼠标移出窗口事件监听
  window.removeEventListener('mouseout', handleMouseOut);
});
</script>

<style scoped>
.local-image-inpainting {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
}

:deep(.el-loading-mask) {
  z-index: 2;
  background-color: rgba(52, 52, 52, 0.788);
  opacity: 1;
  backdrop-filter: blur(3px);
}

.inpainting-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
}

.inpainting-container {
  position: relative;
  width: 90%;
  max-width: 1200px;
  height: 80vh;
  background-color: #2a2d35;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  z-index: 1;
  overflow: hidden;
}

body.dark .inpainting-container {
  background-color: #2a2d35;
  border: 1px solid #3a3d45;
}

.inpainting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #3a3d45;
  background-color: #2a2d35;
}

.inpainting-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
  color: #ffffff;
}

.close-icon {
  cursor: pointer;
  font-size: 20px;
  color: #909399;
}

.close-icon:hover {
  color: #409eff;
}

.inpainting-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.main-image-area {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.canvas-container {
  position: relative;
  flex: 1;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #1a1d23;
}

.canvas-wrapper {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.original-image {
  display: block;
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  pointer-events: none;
}

.mask-canvas {
  position: absolute;
  width: 100%;
  height: 100%;
  cursor: none;
  z-index: 1;
  pointer-events: auto;
}

/* 自定义光标样式 */
.custom-cursor {
  position: fixed;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid #fff;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.5);
  pointer-events: none;
  transform: translate(-50%, -50%);
  z-index: 100;
  display: none;
}

/* 底部控制区域 */
.bottom-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 24px;
  background-color: #2a2d35;
  border-top: 1px solid #3a3d45;
}

.left-tools {
  display: flex;
  align-items: center;
}

.tool-group {
  display: flex;
  gap: 8px;
}

.tool-button {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: #3a3d45;
  border: none;
  color: #ffffff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.tool-button:hover {
  background-color: #4a4d55;
}

.tool-button.active {
  background-color: #409eff;
}

.tool-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  background-color: #3a3d45;
  padding: 8px 16px;
  border-radius: 20px;
}

.zoom-button {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #4a4d55;
  color: #ffffff;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  transition: all 0.2s;
}

.zoom-button:hover {
  background-color: #5a5d65;
}

.zoom-text {
  color: #ffffff;
  font-size: 14px;
  min-width: 40px;
  text-align: center;
}

.right-actions {
  display: flex;
  align-items: center;
}

.generate-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  border: none;
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.generate-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.generate-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 提示词输入区域 */
.prompt-section {
  padding: 16px 24px;
  background-color: #2a2d35;
  border-top: 1px solid #3a3d45;
}

.prompt-input {
  width: 100%;
}

:deep(.prompt-input .el-input__wrapper) {
  background-color: #3a3d45;
  border: 1px solid #4a4d55;
  border-radius: 8px;
  box-shadow: none;
}

:deep(.prompt-input .el-input__inner) {
  color: #ffffff;
  background-color: transparent;
}

:deep(.prompt-input .el-input__inner::placeholder) {
  color: #909399;
}
</style>